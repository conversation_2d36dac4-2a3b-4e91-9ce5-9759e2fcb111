{"mappings": {"properties": {"author": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "author_id": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "author_url": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "category": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "content": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "crawl_ts": {"type": "long"}, "domain": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "embedding": {"type": "dense_vector", "dims": 768, "index": true, "similarity": "cosine", "index_options": {"type": "int8_hnsw", "m": 16, "ef_construction": 100}}, "gid": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "id": {"type": "long"}, "keywords": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "len": {"type": "long"}, "levels": {"type": "object"}, "path": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "post_ts": {"type": "long"}, "protocol": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "q_level": {"type": "long"}, "q_score": {"type": "float"}, "q_tc": {"type": "long"}, "q_user": {"type": "long"}, "s": {"type": "long"}, "site": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "ss": {"properties": {"id": {"type": "long"}, "span": {"type": "long"}}}, "timestamp": {"type": "long"}, "title": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}}}}