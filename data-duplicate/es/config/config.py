from es.config.init import create_es_client
import tomli  # 若使用 Python 3.11+ 可替换为 `import tomllib`
import os
from pathlib import Path


def getRootPath() -> str:
    # 获取当前脚本的绝对路径（兼容符号链接）
    current_file = Path(__file__).resolve()  # 返回 PosixPath 或 WindowsPath 对象
    ROOT_DIR = current_file.parent.parent  # 根据实际层级调整
    return ROOT_DIR

ROOT_DIR = getRootPath()


def load_config(config_path: str = "./config.toml") -> dict:
    """
    从 TOML 文件加载配置
    """
    try:
        with open(config_path, "rb") as f:
            config = tomli.load(f)
            
        # 验证必要配置项存在
        required_keys = ["elasticsearch.host", "elasticsearch.basic_auth"]
        for key in required_keys:
            section, _, subkey = key.partition('.')
            if section not in config or subkey not in config[section]:
                raise ValueError(f"Missing required config key: {key}")
                
        return config
    
    except FileNotFoundError:
        raise FileNotFoundError(f"Config file not found: {config_path}")
    except tomli.TOMLDecodeError as e:
        raise ValueError(f"Invalid TOML format: {e}")



if __name__ == "__main__":

    try:
        # 加载配置
        config = load_config(os.path.join(ROOT_DIR, "config/config.toml"))
        
        # 创建客户端
        es = es.create_es_client(config)
        
        # 测试连接
        print("Cluster Info:", es.info())
        
    except Exception as e:
        print(f"Error: {str(e)}")