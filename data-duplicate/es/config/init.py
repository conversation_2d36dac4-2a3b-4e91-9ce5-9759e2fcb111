from elasticsearch import Elasticsearch
def create_es_client(config: dict) -> Elasticsearch:
    """
    创建 Elasticsearch 客户端
    """
    es_config = {
        "hosts": [config["elasticsearch"]["host"]],
        "basic_auth": (
            config["elasticsearch"]["basic_auth"]["username"],
            config["elasticsearch"]["basic_auth"]["password"]
        ),
        # 若使用自签名证书需添加以下参数
        "verify_certs": False,
        "ssl_show_warn": False
    }
    
    return Elasticsearch(**es_config)


def init():
    create_es_client()