from setuptools import setup, find_packages
 
setup(
    name='data_duplicate',  # 你的包名
    version='0.1.0',      # 版本号
    packages=find_packages(),  # 自动查找包
    install_requires=[  # 依赖列表
        "tomli",
        "elasticsearch",
    ],
    author='wfliu3',  # 作者名
    author_email='<EMAIL>',  # 作者邮箱
    description='ES实时数据去重',  # 描述
    long_description=open('README.md').read(),  # 长描述，可以从README中读取
    long_description_content_type="text/markdown",
    url='https://github.com/yourusername/your_project',  # 项目网址
    classifiers=[  # 分类列表，例如支持的Python版本等
        "Programming Language :: Python :: 3",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
    ],
    python_requires='>=3.6',  # 需要的Python版本
)