from pymilvus import connections, Collection
from pymongo import MongoClient
from elasticsearch import Elasticsearch,helpers
import json
import time

uri = "mongodb://u_lynxiao_read@cjidtRjDCsp1VJ56ecxM@*************:30000,*************:30000,*************:30000/lynxiao_index?authSource=admin"
mongo_client = MongoClient('mongodb+srv://u_lynxiao:<EMAIL>/test?authSource=admin&tls=false&ssl=false')
mongo_db = mongo_client['lynxiao_index']
mongo_collection = mongo_db['lynxiao_554_1_idxkhns0y_dscsrj43_v006_full']


def search_mongo(skip, limit):
    print("search_mongo")
    
    mongo_results = mongo_collection.find_one({"_id": 3152989049515232327})
    print(mongo_results)
    # 分页查询
    # mongo_results = list(mongo_collection.find().skip(skip).limit(limit))
    # mongo_client.close()
    # print(f"查询结果数量: {len(mongo_results)}")
    return mongo_results

def search_milvus_by_ids(ids):
    print("search_milvus_by_ids")
    # 连接到 Milvus 服务器
    connections.connect("default", host="zvkxs4vnwcep.milvus.hf01.dbaas.private", port="32300")

    # 加载集合
    collection = Collection("lynxiao_554_1_idxkhns0y_dscsrj43_v006_full")
    collection.load()

    # 定义查询表达式
    expr = f"id in {ids}"
    output_fields = ["id", "embedding"]

    # 执行查询
    results = collection.query(
        expr=expr,
        output_fields=output_fields
    )

    connections.disconnect("default")
    print(f"milvus查询结果数量: {len(results)}")
    return results

def write_to_file(data, batch_num):
    output_file = f'/data/Projects/data-duplicate/data/merged/merged_results_{batch_num}.json'
    print(f"开始将数据写入文件：{output_file}")
    with open(output_file, 'w', encoding='utf-8') as f:
        for d in data:
            f.write(json.dumps(d, ensure_ascii=False) + '\n')
    print("文件写入完成！")

def write_to_es(data):
    print("开始将数据写入 Elasticsearch")
    es = Elasticsearch(
        ["https://*************:9200"],
        basic_auth=("elastic", "MifeNx0Sr+3rnkj58AbV"),
        verify_certs=False,
        ssl_show_warn=False
    )

    helpers.bulk(es, data)
    es.close()
    print("Elasticsearch 写入完成！")

def run():
    batch_size = 10000
    total_size = 20000000
    total_processed = 0
    
    for offset in range(12400000, total_size, batch_size):
        start_time = time.time()
        print(f"\n开始处理第 {offset//batch_size + 1}/{total_size//batch_size} 批数据...")
        
        # 1. 查询 MongoDB
        mongo_results = search_mongo(offset, batch_size)
        if not mongo_results:
            print("MongoDB 没有更多数据，处理完成！")
            break
            
        # 2. 提取 ID 并查询 Milvus
        mongo_ids = [doc['_id'] for doc in mongo_results]
        milvus_results = search_milvus_by_ids(mongo_ids)
        
        # 3. 合并数据
        merged_data = []
        for mongo_doc in mongo_results:
            milvus_doc = next((doc for doc in milvus_results if doc['id'] == mongo_doc['_id']), None)
            if milvus_doc:
                merged_item = {
                    # '_id': mongo_doc['_id'],
                    'id': mongo_doc['_id'],
                    "_index":"news_index_20250317",
                    'embedding': [float(x) for x in milvus_doc['embedding']],
                    **{k: v for k, v in mongo_doc.items() if k != '_id'}
                }
                merged_data.append(merged_item)
        
        # 4. 写入文件
        write_to_file(merged_data, offset//batch_size + 1)
        
        # # 5. 写入 ES
        # write_to_es(merged_data)
        
        # 更新进度
        total_processed += len(merged_data)
        print(f"当前批次处理完成，已处理总数：{total_processed}/{total_size}")
        print("time",time.time() - start_time)

    print(f"\n所有数据处理完成！")
    print(f"总共处理数据：{total_processed} 条")


if __name__ == "__main__":
    # run()
    search_mongo(0,0)