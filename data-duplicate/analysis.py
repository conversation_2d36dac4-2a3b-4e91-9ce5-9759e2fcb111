import os 
import json 


def gid_dup():
    
    gid_dup_cnt = 0
    rid = 0
    cnt_90, cnt_85,cnt_80,cnt_75,cnt_70 = 0, 0, 0, 0, 0
    cnt_90_gte, cnt_85_gte,cnt_80_gte,cnt_75_gte,cnt_70_gte = 0, 0, 0, 0, 0
    gid_le_90 = 0
    doc_score = {}
    dict_size = 0
    ids = set()
    ids2 = set()
    lines = open("/data/Projects/data-duplicate/data/analysis/recall_results/esvector_recall_0.7.txt","r",encoding="utf8").readlines()
    for line in lines:
        data = json.loads(line) 
        raw_id =  data["基准doc id"]

        if not data["id是否相同"]:
            if raw_id not in doc_score:
                dict_size += 1
                doc_score[raw_id] = [data['召回doc score']]
            else:
                doc_score[raw_id].append(data['召回doc score'])
       
        if not data["id是否相同"] and data["gid是否相同"]:
            # print(data)
            raw_id = data["基准doc id"]
            score = data['召回doc score']
            # if raw_id != rid:
            #     gid_dup_cnt += 1
            #     rid = raw_id
            ids.add(raw_id)
            if score < 1:
                gid_le_90 += 1 
                ids2.add(raw_id)


                # cnt_90 +=1 

            
    print(dict_size)
    gid_dup_cnt = len(ids)
    print("fid_dup_cnt:",gid_dup_cnt)
    print(len(ids2))
    for k, v in doc_score.items():
        # print(k)
        min_score = min(v)
        if min_score >= 0.9:
            cnt_90 += 1
        if min_score >= 0.85 and min_score < 0.9:
            cnt_85 += 1
        if min_score >=0.8 and min_score  < 0.85:
            cnt_80 += 1
        if min_score >= 0.75 and min_score < 0.8:
            cnt_75 += 1
        if min_score >= 0.7 and min_score < 0.75:
            cnt_70 += 1

        if min_score >= 0.9:
            cnt_90_gte+=1
        if min_score >= 0.85:
            cnt_85_gte += 1
        if min_score >= 0.8:
            cnt_80_gte += 1
        if min_score >= 0.75:
            cnt_75_gte += 1
        if min_score >= 0.7:
            cnt_70_gte += 1
        
    print("90:",cnt_90)
    print("85:",cnt_85)
    print("80:",cnt_80)
    print("75:",cnt_75)
    print("70:",cnt_70)

    print(">=90:",cnt_90_gte)
    print(">=85:",cnt_85_gte)
    print(">=80:",cnt_80_gte)
    print(">=75:",cnt_75_gte)
    print(">=70:",cnt_70_gte)

    print(gid_dup_cnt)

gid_dup()