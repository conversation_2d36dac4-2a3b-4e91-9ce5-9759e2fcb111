# 从 es 随即查询10条数据
# 取 每条数据的title + "，"+content 计算向量 或 取文档中的向量
# 根据向量查询es，取top100
# 根据score 去看是不是重复

import random
from elasticsearch import Elasticsearch
import numpy as np
from sentence_transformers import SentenceTransformer

# 初始化 Elasticsearch 客户端
es = Elasticsearch(["http://localhost:9200"])
# 初始化向量模型
model = SentenceTransformer('paraphrase-multilingual-MiniLM-L12-v2')

def random_sample_documents(index_name, size=10):
    """从 ES 随机抽取文档"""
    query = {
        "query": {
            "function_score": {
                "query": {"match_all": {}},
                "random_score": {}
            }
        },
        "size": size
    }
    
    response = es.search(index=index_name, body=query)
    return response["hits"]["hits"]

def get_document_vector(doc):
    """获取文档向量，如果文档中已有向量则直接使用，否则计算"""
    if "vector" in doc["_source"]:
        return doc["_source"]["vector"]
    
    # 拼接标题和内容
    text = f"{doc['_source'].get('title', '')}，{doc['_source'].get('content', '')}"
    # 计算向量
    vector = model.encode(text)
    return vector.tolist()

def vector_search(index_name, vector, size=100):
    """使用向量进行相似度搜索"""
    
    query = {
  "knn": [
    {
      "field": "ss.vector",
      "query_vector": vector,
      "k": 20,
      "num_candidates": 256,
      "inner_hits": {
        "size": 3
      }
    }
  ],
  "size": 20
}
    
    response = es.search(index=index_name, body=query)
    return response["hits"]["hits"]

def check_duplicates(results, threshold=0.9):
    """检查结果中的重复项"""
    duplicates = []
    
    for i in range(len(results)):
        for j in range(i+1, len(results)):
            score_i = results[i]["_score"]
            score_j = results[j]["_score"]
            
            # 如果两个文档的相似度分数接近，可能是重复
            if abs(score_i - score_j) < threshold:
                duplicates.append((i, j, abs(score_i - score_j)))
    
    return duplicates

def main(index_name):
    # 1. 随机抽取10条文档
    random_docs = random_sample_documents(index_name)
    
    for doc in random_docs:
        # 2. 获取文档向量
        vector = get_document_vector(doc)
        
        # 3. 使用向量查询相似文档
        similar_docs = vector_search(index_name, vector)
        
        # 4. 检查重复
        duplicates = check_duplicates(similar_docs)
        
        print(f"原始文档: {doc['_source'].get('title', '')}")
        print(f"找到 {len(similar_docs)} 个相似文档")
        print(f"可能的重复: {len(duplicates)}")
        
        # 打印前5个相似文档
        for i, similar in enumerate(similar_docs[:5]):
            print(f"  {i+1}. {similar['_source'].get('title', '')} (分数: {similar['_score']})")
        
        print("-" * 50)

if __name__ == "__main__":
    main("your_index_name")  # 替换为你的索引名称