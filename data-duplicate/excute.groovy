// Groovy 示例代码
def execute(input) {

    interveneResults = input.arg1   //干预组件结果
    rerankResults =input.arg2       //重排结果

    // 没有设置topk，那topk就是-1
    def topK = input.arg3 ?: -1           //开始节点传入的topK

    def interveneDoc
    // 判断干预结果是否存在有效数据
    boolean hasInterveneData
    interveneResults.each {result ->
        if (result?.docs != null&&result.docs.size()>0){
            interveneDoc = result.docs.first()
            hasInterveneData = true
        }
    }

   if (!hasInterveneData) {
        // 无干预数据，按topK截取重排结果
        if (topK >= 0) {
            rerankResults.each { rerankResult ->
                rerankResult.docs = (rerankResult.docs ?: []).take(topK)
            }
        }
        return [ "results": rerankResults ]
    }

    //判断args2中是否存在url中是否含有baike.baidu.com，
    // 如果有，则删除所有url中包含baidu.baike.com的doc,然后把args1中的结果插入到args1的第二位
    // 如果没有，则直接把args1中的结果插入到rerankResultsdocs的第二位
    rerankResults.each { rerankResult ->
        def rerankDocs = rerankResult.docs ?: []

        // 1. 删除所有百度百科文档（修正域名拼写）
        rerankDocs.removeAll { doc ->
            doc.domain?.contains("baike.baidu.com")
        }

        // 2. 插入干预文档到第二位
        
        if (rerankDocs.size() >= 1) {
            // 逆序插入以保证顺序正确（例如插入[doc1,doc2]时变成 pos1->doc1, pos1->doc2）
                rerankDocs.add(1, interveneDoc)
        } else {
            rerankDocs.add(interveneDoc)
        }
        
        // 3. 按topK截断
        if (topK >= 0 && rerankDocs.size() > topK) {
            rerankResult.docs = rerankDocs.take(topK)
        } else {
            rerankResult.docs = rerankDocs
        }
    }

    return [
            "results": rerankResults
    ]
}