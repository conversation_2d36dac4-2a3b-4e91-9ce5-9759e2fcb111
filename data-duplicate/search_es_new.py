from elasticsearch import Elasticsearch
import warnings
from pymongo import MongoClient
import json

# 忽略不安全的请求警告（仅限测试环境）
warnings.filterwarnings("ignore", category=UserWarning)

# 配置集群连接
# es = Elasticsearch(
#         ["https://10.103.254.65:9200"],
#         basic_auth=("elastic", "MifeNx0Sr+3rnkj58AbV"),
#         verify_certs=False,
#         ssl_show_warn=False
#     )

es = Elasticsearch(
        ["https://10.103.254.31:19200","https://10.103.254.31:19400","https://10.103.254.31:19600"],
        http_auth=("elastic", "MifeNx0Sr+3rnkj58AbV"),
        verify_certs=False,
        ssl_show_warn=False
    )

mongo_client = MongoClient('****************************************************************************************************************************************')
mongo_db = mongo_client['lynxiao_index']
mongo_collection = mongo_db['lynxiao_554_1_idxkhns0y_dscsrj43_v006_full']

es_col = "recalltest3_bbq"
# es_col = "news_index_20250310"
size = 100

def getRandomQuery():
    fp_w = open("/data/Projects/data-duplicate/data/analysis/random_query/random_query.txt", "w")
    IDs = []
    id2vector = {}
    # 随机查询100条数据
    query = {
        "query": {
            "function_score": {
                "random_score": {}  # 使用随机评分
            }
        },
        "size": 500
    }
    response = es.search(index=es_col, body=query)
    # 提取结果
    hits = response['hits']['hits']
    print(f"成功获取 {len(hits)} 条文档:")
    
    # 获取文档IDs
    for i, doc in enumerate(hits):  # 仅显示前3条作为示例
        # print(doc)
        IDs.append(int(doc['_id']))
        print(doc)
        # Embeddings = doc['_source']['embedding']
        Embeddings=doc['_source']['ss']['vector']
        print(Embeddings)
        id2vector[doc['_id']] = Embeddings
    print(IDs)
    docs = mongo_collection.find({"_id": {"$in": IDs}})
    for doc in docs:
        doc['embedding'] = id2vector[str(doc['_id'])]
        fp_w.write(json.dumps(doc,ensure_ascii=False)+"\n")
    
    print(f"\n...（共 {len(hits)} 条文档）")


def serach_es():
    # lines = open('/data/Projects/data-duplicate/data/analysis/random_query/random_query.txt','r', encoding='utf8').readlines()
    file_cnt = 1
    lines = open("/data/Projects/data-duplicate/data/3w_docs/docs_embed.json", 'r', encoding='utf-8')
    fp_w = open(f"/data/Projects/data-duplicate/data/analysis/recall_results/esvector_recall_0.8_{file_cnt}.txt", "w", encoding='utf8')
    index = 1
    batch_size,cnt = 5000, 0
    for line in lines:
        IDs = []
        tmp = {}
        cnt += 1
        data = json.loads(line)
        embedding = data['embedding']
        # 选取前100条数据
        query = {
            "size": size,
            "min_score":0.8,
            "query": {
                "nested": {
                    "path": "ss",
                    "query": {
                        "script_score": {
                            "query": {
                                "knn": {
                                    "field": "ss.vector",
                                    "query_vector": embedding,
                                    "k": size,
                                    "num_candidates": 256
                                }
                            },
                            "script": {
                                "source": "1/(l2norm(params.queryVector, 'ss.vector')+1)",
                                "params": {
                                    "queryVector": embedding
                                }
                            }
                        }
                    }
                }
            }
        }
        # print(query)
        # exit()
        response = es.search(index="news_index_20250324", body=query)
        # 提取结果
        hits = response['hits']['hits']
        print(f"成功获取 {len(hits)} 条文档:")
        # 获取文档IDs
        for i, doc in enumerate(hits):  # 仅显示前3条作为示例
            tmp['index'] = index
            tmp['基准doc id'] = data['_id']
            tmp['基准doc gid'] = data['gid']
            tmp['基准doc url'] = data['url']
            tmp['基准doc title'] = data['title']
            tmp['基准doc content'] = data['content']
            tmp['召回doc id'] = doc['_source']['id']
            tmp['召回doc gid'] = doc['_source']['gid']
            tmp['召回doc score'] = doc['_score']
            tmp['召回doc url'] = doc['_source']['url']
            tmp["召回doc title"] =  doc['_source']['title']
            tmp['召回doc content'] = doc['_source']['content']
            tmp['召回doc post_time'] = doc['_source']['post_time']
            if tmp['基准doc id'] == tmp['召回doc id'] :
                tmp['id是否相同'] = True
            else:
                tmp['id是否相同'] = False

            if tmp['基准doc gid'] == tmp['召回doc gid'] :
                tmp['gid是否相同'] = True
            else:
                tmp['gid是否相同'] = False
        
            fp_w.write(json.dumps(tmp,ensure_ascii=False)+"\n")
            print(f"\n...（共 {len(hits)} 条文档）")
        index += 1
        if cnt % batch_size == 0:
            file_cnt += 1
            fp_w = open(f"/data/Projects/data-duplicate/data/analysis/recall_results/esvector_recall_0.8_{file_cnt}.txt", "w", encoding='utf8')


def search_mongo():
    print("search_mongo")
    lines = open('/data/Projects/data-duplicate/data/analysis/recall_results/esvector_recall_0.8.txt','r', encoding='utf8').readlines()
    fp_w = open("/data/Projects/data-duplicate/data/analysis/recall_results/esvector_recall_0.8_doc.txt", "w", encoding='utf8')
    for line in lines:
        data = json.loads(line)
        for key, value in data.items():
            raw_id = int(key)
            print(raw_id)
            for item in value:
                tmp = {}
                mongo_results = mongo_collection.find_one({"_id": raw_id})
                for k, v in item.items():
                    _id = int(k)
                    result = mongo_collection.find_one({"_id": _id})
                    title = result['title']
                    content = result['content']
                    url = f"{result['protocol']}://{result['domain']}{result['path']}"
                    tmp['raw_id'] = raw_id
                    tmp['_id'] = _id
                    tmp['url'] = url
                    tmp['title'] = title
                    tmp['content'] = content
                    tmp['score'] = v
                    fp_w.write(json.dumps(tmp,ensure_ascii=False)+"\n")


def test_search():
    embedding = [0.044528823,-0.000068864865,-0.01064056,0.02226532,-0.015735202,-0.0258375,0.000030069581,-0.0010670795,0.05356118,-0.06436058,-0.0099641755,-0.06260327,-0.0891054,-0.0096650515,-0.01969495,-0.0024125522,0.07004999,0.071452044,0.0102645,-0.06299917,0.051156685,0.057414092,0.0060294387,0.051752616,0.0297661,-0.017879311,-0.031380173,0.024751125,0.016189551,-0.020197073,-0.09727771,-0.049086206,0.01138931,0.0032300856,0.020379242,-0.01811901,0.06613505,0.06351233,-0.014131471,0.01413294,-0.060107995,-0.07512403,-0.015536248,0.048859578,-0.020935358,0.06518186,-0.033580884,0.039389748,-0.028171541,0.0691069,0.00903934,-0.06470931,0.031683777,-0.009374693,-0.01996968,-0.018685816,-0.0027145292,0.0757252,0.06452571,-0.03125757,-0.008698462,-0.0026595758,0.035777654,0.015418772,-0.020885792,-0.03308767,0.01959757,0.014263056,0.0915821,0.06151254,0.031410284,0.071986586,-0.009059473,-0.021907145,0.0013375172,0.035644628,-0.021012617,0.03346852,-0.053673744,0.037665397,0.019882446,0.0064880503,-0.012771557,0.06251636,0.005387322,0.0022846423,0.05199638,-0.029608833,0.045232616,-0.022294136,0.03074662,0.05263683,0.03329683,0.021439485,0.046581034,0.03211504,-0.04003056,0.037488982,0.03733578,0.022254702,0.016350104,-0.0107357,-0.0009908896,-0.011873593,0.012941939,-0.013273161,0.0052050413,0.00515799,0.011899843,0.0055458494,-0.011662061,-0.028925156,0.005638454,-0.025342392,-0.027364632,0.059635505,-0.04501309,-0.09885485,0.0076920693,-0.0069299294,-0.037449468,-0.0636496,-0.014690075,-0.0022201615,-0.016865151,-0.013425628,-0.042725775,-0.06478564,-0.050224565,-0.0055279415,0.060588647,0.0101515185,0.047139738,-0.037584238,-0.01730749,0.046320897,-0.058797467,0.050534595,0.033882927,-0.06510919,-0.064832546,0.004163458,-0.030337086,0.054295525,0.004630945,0.025318688,-0.029403767,-0.013853447,-0.11496818,0.014575128,-0.09449475,0.015835652,0.029715676,-0.028771326,0.00060644076,-0.05773275,-0.07559521,0.035846684,-0.08793975,-0.006857659,0.016273728,0.041304413,0.01686561,-0.0329594,-0.022007583,-0.011229653,0.009618272,0.039568134,0.0073767733,0.0116393715,-0.04133566,0.00091253436,0.022331346,-0.012948872,-0.017495206,0.04801382,0.018565608,-0.0021154226,-0.046128497,0.033704486,-0.011041375,-0.02985724,0.024650382,-0.03584254,0.014006666,0.013436625,-0.051730342,-0.031910133,-0.031455614,-0.0016245666,-0.012209833,-0.0038495825,-0.063562416,0.012245515,-0.02272515,-0.046942327,-0.023731714,-0.09756414,0.014483658,-0.003732442,-0.030914577,0.026155706,-0.016606787,-0.0042971405,-0.010044009,0.0041675284,-0.006911499,-0.0057367366,-0.05865349,-0.04448752,0.021026213,-0.0255011,0.029582841,-0.001730482,0.016294518,0.0030092897,0.014768555,0.00086052617,0.011639461,-0.018718502,-0.0041054157,0.0061930073,-0.046329737,0.021874998,0.029966705,0.009423475,-0.012803328,0.011878914,0.022965457,0.06510305,0.000885023,0.004931874,0.017666778,0.003259879,-0.02850258,0.015795467,0.07261409,0.03343092,-0.0023676846,0.033098552,0.010088193,0.021454016,0.030826578,0.036878586,-0.02247263,-0.05027835,-0.040923644,0.028859092,-0.007172195,0.019078605,0.026136354,0.020628827,-0.0216858,0.062367342,0.0016200027,-0.019650424,0.0020899915,0.022561265,0.027403131,0.025003677,-0.042809416,-0.02162434,0.018555343,-0.024363933,0.020937499,-0.042702682,0.01991509,0.04345972,-0.10154465,0.052310973,-0.025580052,0.019234529,0.007610818,0.004204295,-0.01667849,0.028630743,0.036871135,0.02128662,0.041241836,0.0031159176,0.0013449872,-0.002139656,-0.02453055,0.033337608,-0.037454087,-0.048154503,0.017818896,-0.019019851,0.0032578446,0.056157365,-0.027797777,-0.000733363,0.0015336891,0.03496863,-0.019320982,-0.020641996,-0.035594497,0.058994602,0.035359986,0.00963223,-0.022859583,0.0070134983,-0.021860631,-0.07071431,-0.04618234,-0.01410286,-0.043328766,-0.056875303,-0.03378065,0.026106114,0.042363793,-0.009621358,-0.0063381055,0.005686046,0.09701262,0.015574829,-0.017218407,0.019452015,0.0061748074,-0.019444903,-0.01293327,0.0012344467,-0.07713203,0.030162733,0.002849046,-0.041188978,0.05195371,0.012159678,0.0071395724,-0.0075665484,-0.017450305,0.018620195,0.01839757,-0.009667784,0.00296872,-0.024399547,0.026806802,0.021999901,0.022354335,0.009233706,0.04523766,0.006853752,-0.07528396,-0.033661284,0.0069176946,-0.00036752276,0.034247946,0.029536508,0.014559566,-0.016926318,-0.007891637,0.05838264,0.038950473,0.040375005,0.041304328,-0.05452597,-0.024452211,0.017765565,-0.055344112,0.021876942,0.055546224,-0.0043726657,0.0028671927,0.024542203,-0.03497285,-0.05483714,-0.027148675,-0.004436845,-0.05449805,0.049100615,0.004268967,-0.007266023,-0.016707703,0.07449777,-0.029650863,0.024704346,0.0014038553,0.08214011,0.014029383,0.04138758,0.012567622,-0.018881653,-0.020661779,-0.06395653,0.003652639,0.08635679,-0.0311285,0.008977583,-0.00928607,-0.03564325,-0.0016821533,-0.01739648,-0.048270468,0.0024473357,0.049613267,-0.01507856,0.046855774,-0.008698706,0.029801883,0.02531498,0.046367172,0.06304887,-0.01893013,0.019294431,0.10934489,0.07024386,-0.006626682,0.007767514,0.04038357,0.0040008374,-0.010394988,0.049587924,0.021931695,-0.0008657871,-0.026740491,0.04877111,0.04275356,0.065775156,0.13763998,-0.011637588,-0.002603912,0.015564336,0.01832461,-0.029523011,-0.04563258,0.06335051,0.0045519243,0.04574539,0.040841598,0.08823826,0.032205418,0.10359947,-0.08508449,0.013574424,-0.02914771,0.028947907,0.021703037,-0.017473683,0.006059712,-0.035917323,-0.01617609,0.025135724,0.004972566,0.0071358546,-0.013208711,-0.013729895,0.037203103,0.033149112,-0.025449524,0.0010056623,-0.026020959,-0.060530916,-0.011703248,-0.047827516,0.03925759,0.012265056,-0.010584189,-0.0130599905,-0.015918734,-0.040949512,-0.007932335,-0.0025795249,-0.014231698,0.010712043,0.07479472,0.0070002023,0.01202655,-0.018379813,-0.016581573,0.06970125,0.034153976,0.0073342253,0.051704448,0.011269674,-0.036485795,-0.039751463,-0.037893094,-0.037766505,0.033962432,0.023277145,0.057410944,0.0397799,0.034288652,-0.029704086,-0.01932288,0.031406,-0.033967994,0.02741159,0.0019730548,0.03797652,0.04859317,0.04092386,-0.058292866,-0.018967068,0.017973086,0.027768444,0.019208454,-0.01668141,0.05617781,0.031212058,-0.03140506,-0.019327797,0.01242073,-0.0016844069,0.018664429,-0.042374402,0.011785014,0.011391289,0.02008188,0.039996408,-0.027982619,-0.05475655,-0.03175679,-0.026614733,-0.07582679,0.029644877,-0.05798489,0.0656539,-0.06793082,0.008916276,0.028940247,-0.01180945,-0.04453914,0.033478547,0.012426371,0.021468151,-0.0063457843,0.022347143,0.012443166,-0.0348455,-0.037689026,0.080835454,0.02203469,0.0067108744,0.03499733,0.0008954666,0.020335954,-0.039226264,0.024787571,-0.022084117,-0.0030004946,0.024861759,0.010884225,0.010587106,0.006088727,0.010721,0.04482845,-0.05436376,-0.026996478,0.0014562419,0.04063624,0.023472933,0.08622917,0.049559157,-0.032382842,-0.05810653,-0.015107227,-0.0015425642,0.008433188,0.048855368,-0.0010124159,0.00883965,-0.03230039,-0.033048935,-0.028887138,0.014905846,0.036234695,-0.026697338,0.04869335,0.023193546,-0.004654057,-0.01998485,-0.0061492426,-0.10244717,-0.008910422,-0.03332233,-0.024631266,-0.09226413,-0.0066780113,0.008994895,-0.043334655,0.0011920833,0.006721601,-0.026385764,-0.023162013,-0.022744644,-0.01635993,0.019637467,-0.02347984,0.02917278,0.020506876,-0.0015109254,-0.024335885,0.009125231,0.038153596,0.019092968,0.020313239,-0.069265425,-0.01156404,-0.020744476,0.027998315,-0.03857018,0.013823801,-0.009140843,-0.018747004,-0.01386622,-0.010203202,-0.029969579,-0.051447477,0.0066001387,-0.0038961172,-0.02875453,-0.0022426937,0.03445051,-0.03132924,0.03275804,0.018776776,0.037567835,-0.029198188,0.030991647,-0.015349544,-0.015521532,-0.0016274626,-0.03193038,0.043701444,0.021062199,-0.041948967,-0.01970144,-0.03749372,-0.0238024,0.04131337,-0.026299348,0.012017286,-0.03552059,-0.074450925,0.002153748,-0.030456176,-0.049431276,-0.06858039,-0.0722712,0.029157216,0.04530045,-0.00092314347,0.024831472,0.03378355,-0.003007242,0.026507076,-0.01884264,-0.0031395082,0.009805775,0.0356551,0.014315864,-0.022213362,0.032786608,-0.030479128,-0.006710097,0.0062670433,-0.017490242,0.036779452,0.05021693,0.0010951904,0.048612855,-0.014616597,-0.03133194,-0.049437102,0.015643334,0.04105981,-0.039431922,-0.010113841,0.032888964,-0.013824024,0.019765608,0.045962762,0.014437938,-0.08314499,-0.06910219,-0.04795568,-0.053851046,-0.039666764,0.009227399,-0.0088500995,-0.015152327,-0.069512255,0.010574428,-0.03238178,-0.050439257,0.013408418,0.008659977,0.013575963,0.0078970175,0.01518295,0.003288518,-0.015774645,-0.021466574,0.028426617,-0.0065890555,0.019054446,0.055925954,-0.047667585,0.011719509,0.051118206,-0.03486605,0.008482388,0.0033895192,-0.0048314985,-0.034476504,0.073411286,-0.0076348847,0.0003840917,0.051457133,0.032191604,0.05316354,-0.020098906,0.0612597,-0.025963325,-0.026874453,0.005884856,0.056238793,-0.003006363,0.0873464,0.04077494,-0.036554597,-0.03432084,0.010229529,0.009440334,-0.028761195,0.040415592,0.051810842,0.021344882,-0.02735148,0.029779363,-0.016200801,0.013248023,-0.010843244,-0.016785432,-0.02887826,-0.039475095,-0.0201753,0.00728724,-0.009624303,0.019373912,-0.017643627,-0.03366858,0.044629186,-0.016696855,0.004026833,0.059408184,0.08469065,0.008491577,0.03863811,-0.040180065,0.033373054,0.022341454,0.051698484,0.025469473,0.005638077,-0.022484206,0.0011660334,-0.03859583,0.009579117,-0.026377823,0.03541695,0.009966825,-0.022522544,-0.002814565,0.01142944,-0.05949278,0.04454382]
    # query = {
    #     "size": 20,
    #     "query": {
    #         "nested": {
    #             "path": "ss",
    #             "query": {
    #                 "script_score": {
    #                     "query": {
    #                         "knn": {
    #                             "field": "ss.vector",
    #                             "query_vector": embedding,
    #                             "k": 20,
    #                             "num_candidates": 256
    #                         }
    #                     },
    #                     "script": {
    #                         "source": "1/(l2norm(params.queryVector, 'ss.vector')+1)",
    #                         "params": {
    #                             "queryVector": embedding
    #                         }
    #                     }
    #                 }
    #             }
    #         }
    #     }
    # }
    query = {
        "query": {
            "bool": {
                "should": [
                    {
                        "nested": {
                            "path": "ss",
                            "query": {
                                "script_score": {
                                    "query": {
                                        "knn": {
                                            "field": "ss.vector",
                                            "query_vector": embedding,
                                            "k": 20,
                                            "num_candidates": 256
                                        }
                                    },
                                    "script": {
                                        "source": "1/(l2norm(params.queryVector, 'ss.vector')+1)",
                                        "params": {
                                            "queryVector": embedding
                                        }
                                    }
                                }
                            }
                        }
                    },
                    {
                    "term": {
                        "gid": "2832638317c56a5b97c7e852aca16b037e3350043a83d6fa4c6f4ee8f213cbff"
                    }
                }
            ],
            "minimum_should_match": 1
            }
        }
    }
    response = es.search(index="news_0325", body=query)
    # 提取结果
    hits = response['hits']['hits']
    for i, doc in enumerate(hits):  # 仅显示前3条作为示例
        print(doc)        
        exit()
    print(f"成功获取 {len(hits)} 条文档:")
    

if __name__ == "__main__":
    # getRandomQuery()
    # serach_es()
    # search_mongo()
    test_search()