import hashlib
import json
import pandas as pd
import os
import re
from openpyxl.utils import escape
def json_to_excel(in_file, out_file):
    print("json_to_excel")

    def clean_text(text):
        if isinstance(text, str):
            text = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f]', '', text)
            text = text.translate(str.maketrans({
                '\u200b': ' ',  '\u00a0': ' ',  '\ufeff': ' '
            }))
            text = text.encode('utf-8', 'ignore').decode('utf-8')
            # 可选：添加单引号禁用超链接（根据需要选择）
            # if text.startswith(('http://', 'https://')):
            #     text = "'" + text
        return text

    with open(in_file, 'r', encoding='utf-8') as f:
        result = []
        for line in f:
            try:
                data = json.loads(line)
                cleaned_data = {
                    k: clean_text(v) if isinstance(v, str) else v 
                    for k, v in data.items()
                }
                result.append(cleaned_data)
            except json.JSONDecodeError:
                print(f"跳过无效JSON行: {line[:50]}...")

    df = pd.DataFrame(result)
    str_cols = df.select_dtypes(include=['object']).columns
    df[str_cols] = df[str_cols].applymap(clean_text)

    with pd.ExcelWriter(out_file, engine='xlsxwriter') as writer:
        df.to_excel(writer, index=False)
        workbook = writer.book
        worksheet = writer.sheets['Sheet1']
        text_format = workbook.add_format({'num_format': '@'})  # 文本格式
        for column in df.select_dtypes(include=['object']).columns:
            col_idx = df.columns.get_loc(column)
            worksheet.set_column(col_idx, col_idx, 30, text_format)


def split_file():
    rid = 0
    batch_size = 4000
    batch = 0
    input_file = "/data/Projects/data-duplicate/data/analysis/recall_results/esvector_recall_0.8.txt"
    output_dir = "/data/Projects/data-duplicate/data/analysis/recall_results/split/"
    batch_cnt = 0
    output_file = f"{output_dir}esvector_recall_0.8_{batch}.txt"
    fp_w = open(output_file, 'w', encoding='utf8')
    try:
        # 读取输入文件
        with open(input_file, 'r', encoding='utf8') as fp_r:
            lines = fp_r.readlines()

        # 处理每一行
        for idx, line in enumerate(lines):
            try:
                data = json.loads(line)
                raw_id = data['raw_id']

                # 如果是一个新文档，更新 rid
                if raw_id != rid:
                    rid = raw_id
                    batch_cnt += 1

                # 如果是一个新批次，创建新文件
                if batch_cnt == batch_size:
                    batch += 1
                    output_file = f"{output_dir}esvector_recall_0.8_{batch}.txt"
                    fp_w = open(output_file, 'w', encoding='utf8')
                    batch_cnt = 0

                # 写入当前行
                data = json.loads(line)
                data['index'] = batch_cnt
                fp_w.write(json.dumps(data, ensure_ascii=False) + '\n')

            except json.JSONDecodeError:
                print(f"Error decoding JSON on line {idx + 1}: {line.strip()}")
            except KeyError:
                print(f"Missing 'raw_id' field on line {idx + 1}: {line.strip()}")

    except FileNotFoundError:
        print(f"Input file not found: {input_file}")
    except Exception as e:
        print(f"An unexpected error occurred: {e}")
    finally:
        # 确保最后一个文件被关闭
        if 'fp_w' in locals():
            fp_w.close()     


def merge_excel(input_dir, output_file):
    # 获取目录下所有 Excel 文件
    excel_files = [f for f in os.listdir(input_dir) if f.endswith(".xlsx") or f.endswith(".xls")]

    # 创建一个 ExcelWriter 对象，用于写入多个 Sheet
    with pd.ExcelWriter(output_file, engine="openpyxl") as writer:
        for file in excel_files:
            # 读取 Excel 文件
            file_path = os.path.join(input_dir, file)
            df = pd.read_excel(file_path)

            # 将文件内容写入新的 Sheet，Sheet 名称为文件名（不带扩展名）
            sheet_name = os.path.splitext(file)[0]  # 关键修复：取元组第一个元素
            df.to_excel(writer, sheet_name=sheet_name, index=False)

    print(f"合并完成，结果已保存到 {output_file}")

if __name__ == '__main__':
    # json_to_excel('/data/Projects/data-duplicate/data/analysis/recall_results/esvector_recall_0.8.txt', 
    #               '/data/Projects/data-duplicate/data/analysis/recall_results/esvector_recall_0.8.xlsx')
    # pass
    # split_file()
    # files = os.listdir("/data/Projects/data-duplicate/data/analysis/recall_results/split_0.8")
    # files = [ os.path.join("/data/Projects/data-duplicate/data/analysis/recall_results/split_0.8", file) for file in files]
    # for file in files:
    #     print(file)
    #     json_to_excel(file, file.replace(".txt", ".xlsx"))
    merge_excel("/data/Projects/data-duplicate/data/analysis/recall_results/split_0.8", "/data/Projects/data-duplicate/data/analysis/recall_results/vector_recall_0.8_merged.xlsx")