import os
import json

import json

def process_file(file_path, fp_w):
    lines = open(file_path, "r", encoding="utf8").readlines()
    for line in lines:
        try:
            data = json.loads(line)
            for d in data:
                fp_w.write(str(d['id']) + "\n")
        except Exception as e:
            print(f"Error processing file {file_path}: {e}")


if __name__ == '__main__':
    dir_path = "/data/Projects/data-duplicate/data/raw/zixun_split"
    
    files = os.listdir("/data/Projects/data-duplicate/data/embedding")
    for file in files:
        process_file()