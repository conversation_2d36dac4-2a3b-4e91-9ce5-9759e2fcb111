import json
import logging
# from tenacity import retry, stop_after_attempt, wait_exponential
from elasticsearch import Elasticsearch, helpers


schema = {
    "mappings": {
        "dynamic": "false",
        "properties": {
            "content": {
                "type": "keyword",
                "index": False,
                "doc_values": False
            },
            "gid": {
                "type": "keyword"
            },
            "id": {
                "type": "long"
            },
            "path": {
                "type": "keyword"
            },
            "post_ts": {
                "type": "long"
            },
            "protocol": {
                "type": "keyword"
            },
            "site": {
                "type": "keyword"
            },
            "len": {
                "type": "integer"
            },
            "domain":{
                "type": "keyword"
            },
            "ss": {
                "type": "nested",
                "properties": {
                    "content_terms": {
                        "type": "text",
                        "analyzer": "my_custom_analyzer"
                    },
                    "embedding": {
                        "type": "dense_vector",
                        "dims": 768,
                        "index": True,
                        "similarity": "l2_norm",
                        "index_options": {
                            "type": "bbq_hnsw",
                            "m": 32,
                            "ef_construction": 256
                        }
                    },
                    "id": {
                        "type": "long"
                    },
                    "span": {
                        "type": "integer"
                    }
                }
            },
            "q_user": {
                "type": "integer"
            },
            "q_level": {
                "type": "integer"
            },
            "timestamp": {
                "type": "long"
            },
            "q_tc": {
                "type": "integer"
            },
            "q_score": {
                "type": "float"
            },
            "s": {
                "type": "integer"
            },
            "crawl_ts": {
                "type": "long"
            },
            "levels": {
                "type": "object",
                "enabled": False
            },
            "title": {
                "type": "keyword",
                "index": False,
                "doc_values": False
            },
            "title_terms": {
                "type": "text",
                "analyzer": "my_custom_analyzer"
            },
            "update_ts": {
                "type": "long"
            },
            "url": {
                "type": "keyword"
            }
        }
    },
    "settings": {
        "index": {
            "refresh_interval": "5s",
            "number_of_shards": "24",
            "translog": {
                "flush_threshold_size": "16GB",
                "sync_interval": "60s",
                "durability": "async"
            },
            "merge": {
                "policy": {
                    "max_merged_segment": "10GB"
                }
            },
            "store": {
                "preload": [
                    "nvd",
                    "dvd",
                    "tim",
                    "doc",
                    "dim",
                    "vex",
                    "veb"
                ]
            },
            "analysis": {
                "analyzer": {
                    "my_custom_analyzer": {
                        "filter": [
                            "lowercase"
                        ],
                        "char_filter": [
                            "punctuation_filter"
                        ],
                        "type": "custom",
                        "tokenizer": "whitespace"
                    }
                },
                "char_filter": {
                    "punctuation_filter": {
                        "pattern": "[\\p{Punct}\\pP]",
                        "type": "pattern_replace",
                        "replacement": " "
                    }
                }
            },
            "number_of_replicas": 1
        }
    }
}


# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def get_es_client():
    """创建可复用的ES连接"""
    return Elasticsearch(
        ["https://10.103.254.65:9200"],
        basic_auth=("elastic", "MifeNx0Sr+3rnkj58AbV"),
        verify_certs=False,
        ssl_show_warn=False
    )

# @retry(stop=stop_after_attempt(3), wait=wait_exponential())
def bulk_insert(es_client, actions):
    """带重试机制的批量插入"""
    try:
        helpers.bulk(es_client, actions)
        logger.info(f"成功插入 {len(actions)} 条文档")
    except helpers.BulkIndexError as e:
        logger.error(f"部分文档插入失败: {str(e)}")
        # 这里可以添加失败处理逻辑

def process_file(file_path, index_name, batch_size=1000):
    """处理文件并分批插入"""
    es = get_es_client()
    batch = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line in f:
                doc = json.loads(line.strip())
                _id = doc['_id']
                del doc['_id']
                d = doc
                # d['id'] = _id
                # d['ss'] = {
                #     "vector": doc['embedding'],
                #     "id": _id,
                # }
                batch.append({
                    "_op_type": "index",
                    "_index": index_name,
                    "_source": d,
                    "_id":d["_id"]
                })
                
                if len(batch) >= batch_size:
                    bulk_insert(es, batch)
                    batch = []
            
            if batch:  # 处理剩余数据
                bulk_insert(es, batch)
                
    except Exception as e:
        logger.error(f"文件处理失败: {str(e)}")
        raise
    finally:
        es.close()


def create_or_recreate_index(es: Elasticsearch, index_name: str, schema: dict) -> bool:
    """
    创建或重建 Elasticsearch 索引
    :param es: Elasticsearch 客户端实例
    :param index_name: 索引名称
    :param schema: 索引的 Mapping 和 Settings 定义
    :return: 是否成功
    """
    try:
        # 1. 检查索引是否存在
        if es.indices.exists(index=index_name): 
            logger.info(f"索引 {index_name} 已存在，执行删除...")
            # 安全删除索引（忽略不存在的索引报错）
            es.indices.delete(
                index=index_name,
                ignore_unavailable=True,
                allow_no_indices=True
            )
            logger.info(f"索引 {index_name} 删除完成")

        # 2. 创建新索引
        es.indices.create(
            index=index_name,
            body=schema
        )
        logger.info(f"索引 {index_name} 创建成功")
        return True

    except ConnectionError:
        logger.error("连接到 Elasticsearch 失败，请检查网络或认证")
        return False
    except Exception as e:
        logger.error(f"未知错误: {str(e)}")
        return False

if __name__ == "__main__":
    es = get_es_client()
    create_or_recreate_index(es, "news_index_20250421", schema)
    process_file("/data/Projects/data-duplicate/data/3w_docs/docs_embed_demo.json", "news_index_20250421")

    