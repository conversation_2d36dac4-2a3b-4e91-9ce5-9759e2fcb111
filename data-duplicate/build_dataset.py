import os
import json


src_dir = "/data/Projects/data-duplicate/data/analysis/recall_results/split_0.8"
dst_dir = "/data/Projects/data-duplicate/data/analysis/recall_results/dataset"
files = os.listdir(src_dir)

for file in files:
    if '.txt' in file:
        file_path = os.path.join(src_dir, file)
        
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()

         # 将修改后的内容写回文件
        with open(os.path.join(dst_dir, file), 'w', encoding='utf-8') as fw:
        
            for line in lines:
                # 解析JSON数据
                data = json.loads(line.strip())
                
                # 创建新字典用于存储修改后的键值对
                modified_data = {}
                for key in data:
                    # 替换基准doc相关key
                    if key.startswith("基准doc"):
                        new_key = "doc1" + key[len("基准doc"):]
                    # 替换召回doc相关key
                    elif key.startswith("召回doc"):
                        new_key = "doc2" + key[len("召回doc"):]
                    # 其他键保持不变
                    else:
                        new_key = key
                    modified_data[new_key] = data[key]
                
                # 将修改后的数据转换为JSON字符串
                new_line = json.dumps(modified_data, ensure_ascii=False) + '\n'
                # 写入文件
                fw.write(new_line)