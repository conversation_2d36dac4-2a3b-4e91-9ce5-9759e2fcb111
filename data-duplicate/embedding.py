import os
import json
import asyncio
import aiofiles
from concurrent.futures import ThreadPoolExecutor
from pymilvus import connections, Collection
from datetime import datetime
# 连接到 Milvus 服务器
connections.connect("default", host="zvkxs4vnwcep.milvus.hf01.dbaas.private", port="32300")

# 加载集合
collection = Collection("lynxiao_554_1_idxkhns0y_dscsrj43_v006_full")
collection.load()

async def process_file(file_path, fp_w, semaphore):
    async with semaphore:  # 控制并发数量
        async with aiofiles.open(file_path, mode='r', encoding='utf8') as f:
            try:
                print(f"Processing file {file_path}")
                data = json.loads(await f.read())
                for d in data:
                    await fp_w.write(str(d['id']) + "\n")
                f.close()
            except Exception as e:
                print(f"Error processing file {file_path}: {e}")
                f.close()

async def save_embedding_from_merged_async(max_concurrency=10):
    semaphore = asyncio.Semaphore(max_concurrency)  # 设置最大并发数量
    async with aiofiles.open("/data/Projects/data-duplicate/data/has_embedding_ids.txt", "w", encoding="utf8") as fp_w:
        files = os.listdir("/data/Projects/data-duplicate/data/embedding")
        tasks = []
        for file in files:
            if file.endswith(".json"):
                file_path = os.path.join("/data/Projects/data-duplicate/data/embedding", file)
                tasks.append(process_file(file_path, fp_w, semaphore))
        await asyncio.gather(*tasks)






def save_embedding_from_merged():
    fp_w = open("/data/Projects/data-duplicate/data/has_embedding_ids.txt", "w", encoding="utf8")
    files = os.listdir("/data/Projects/data-duplicate/data/embedding")
    for file in files:
        if file.endswith(".json"):
            with open(os.path.join("/data/Projects/data-duplicate/data/embedding",file), "r", encoding="utf8") as f:
                try:
                    data = json.load(f)
                    for d in data:
                        fp_w.write(str(d['id'])+"\n")
                except:
                    print(f)    
            f.close()


def save_hasEmbedding_ids():
    files = os.listdir("/data/Projects/data-duplicate/data/embedding")
    fp_w = open("/data/Projects/data-duplicate/data/has_embedding_ids_async.txt", "w", encoding="utf8")


def detect_encoding(file_path):
    with open(file_path, 'rb') as f:
        raw_data = f.read(10000)  # 读取部分内容来检测编码
    result = chardet.detect(raw_data)
    return result['encoding']

def dup_embedding_ids():
    fp_w = open("/data/Projects/data-duplicate/data/has_embedding_ids_dup.txt", 'w',encoding='utf8')
    lines = open("/data/Projects/data-duplicate/data/has_embedding_ids.txt", "r", encoding="utf-8", errors="ignore").readlines()  # 忽略无法解码的字
    for line in lines:
        fp_w.write(line)


IDs = set()

def search_milvus_by_ids(ids):
    print("search_milvus_by_ids")
    
    # 定义查询表达式
    expr = f"id in {ids}"
    output_fields = ["id", "embedding"]

    # 执行查询
    results = collection.query(
        expr=expr,
        output_fields=output_fields
    )

    print(f"milvus查询结果数量: {len(results)}")
    return results

def search_milvus(file):
    IDs = []
    print(f"file:{file}")
    timsstamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    batch_size = 20
    ids = []
    mongo_docs = {}
    # 定义查询表达式
    expr = f"id in {ids}"
    output_fields = ["id", "embedding"]
    # print(expr)

    output_file = os.path.join("/data/Projects/data-duplicate/data/raw/embedding", f"{file.split('/')[-1]}_{timsstamp}")
    with open(file, "r", errors="ignore") as f:
        lines = f.readlines()  # ✅ 正确读取所有行
    try:
        with open(output_file, 'w', encoding='utf8') as fp_w:
            for line in lines:
                # print(line)
                # print(line, flush=True)  # 添加 flush=True
                try:
                    d = json.loads(line)
                except json.JSONDecodeError as e:
                    print(f"JSON 解析失败: {e}", flush=True)
                    continue  # 跳过错误行
                _id = d['_id']
                # print(_id)
                if _id not in IDs:
                    mongo_docs[_id] = d
                    # results = search_milvus_by_ids([d['_id']])
                    # print("search_milvus_by_ids")
                    ids.append(_id)
                    if len(ids) == batch_size:
                        # 执行查询
                        expr = f"id in {ids}"
                        results = collection.query(
                            expr=expr,
                            output_fields=output_fields
                        )
                        # print(len(results))
                        # print(results, flush=True)
                        # print("===result===")
                        for result in results:
                            result_id = result['id']
                            embedding = result['embedding']
                            if result_id in mongo_docs:
                                mongo_docs[result_id]['embedding'] = [float(x) for x in embedding]
                                fp_w.write(json.dumps(mongo_docs[result_id], ensure_ascii=False) + '\n')
                        print(f"search_milvus_by_ids,results` len: {len(results)},len:{len(ids)}")
                        
                        ids = []
                        mongo_docs = {}
            # 执行查询, 剩余的id
            if len(ids) > 0:
                # 执行查询
                expr = f"id in {ids}"
                results = collection.query(
                    expr=expr,
                    output_fields=output_fields
                )
                for result in results:
                    # print(result)
                    result_id = result['id']
                    embedding = result['embedding']
                    if result_id in mongo_docs:
                        mongo_docs[result_id]['embedding'] = [float(x) for x in embedding]
                        # print(mongo_docs[result_id])
                        fp_w.write(json.dumps(mongo_docs[result_id], ensure_ascii=False) + '\n')
                print(f"search_milvus_by_ids,results` len: {len(results)},len:{len(ids)}")
                ids = []
                mongo_docs = {}

    except Exception as e:
        print(f"处理文件 {file} 时发生致命错误: {e}", flush=True)


def search_embedding():
    # 读取已有embedding的id
    # lines = open("/data/Projects/data-duplicate/data/has_embedding_ids.txt", "r", encoding="utf-8", errors="ignore").readlines()  # 忽略无法解码的字
    # for line in lines:
    #     try:
    #         IDs.add(int(line.strip()))
    #     except:
    #         # print(line)
    #         pass
    # print("ids len: ", len(IDs))

    files = os.listdir("/data/Projects/data-duplicate/data/raw/zixun_split")
    files = [os.path.join("/data/Projects/data-duplicate/data/raw/zixun_split",f) for f in files]
    print(files)
    
    executor =  ThreadPoolExecutor(max_workers=20) 
    for file in files:
        executor.submit(search_milvus, file)
    executor.shutdown(wait=True)  # ✅ 等待所有任务完成
    # for file in files:
    #     search_milvus(file)




if __name__ == "__main__":
    # save_embedding_from_merged()
    # 运行协程，设置最大并发数量为 5
    # asyncio.run(save_embedding_from_merged_async(max_concurrency=10))
    # dup_embedding_ids()
    print(collection)
    search_embedding()
    # lines = open("/data/Projects/data-duplicate/data/split/aa", "r", encoding="utf-8", errors="ignore").readlines()  # 忽略无法解码的字
    # for line in lines:
    #     print(line)

    
    
    # 定义查询表达式
    # ids = [4007077556653835241,619317838033679222]
    # ids = [1848597776564462718, 1848599570036839841, 1848603487268262793, 8485334520162839843, 8485343465821163192, 8485349178931968130, 8485353618656238622, 8485376886278478327, 8485416721131078676, 8485328284668079215]
    # expr = f"id in {ids}"
    # output_fields = ["id", "embedding"]
    # results = collection.query(
    #     expr=expr,
    #     output_fields=output_fields
    # )
    # print(len(results))
    # for result in results:
    #     # print(result)
    #     print(result['id'])