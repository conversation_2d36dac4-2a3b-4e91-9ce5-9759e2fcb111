
from elasticsearch import Elasticsearch,helpers
import json

def write_to_es(data):
    print("开始将数据写入 Elasticsearch")
    es = Elasticsearch(
        ["https://10.103.254.65:9200"],
        basic_auth=("elastic", "MifeNx0Sr+3rnkj58AbV"),
        verify_certs=False,
        ssl_show_warn=False
    )

    helpers.bulk(es, data)
    es.close()
    print("Elasticsearch 写入完成！")


if __name__ == "__main__":
    printi  = False
    for i in range(1502, 2001):
        update_buffer = []
        print("merged_results_"+str(i)+".json")
        with open("merged_results_"+str(i)+".json", 'r', encoding='utf-8') as f:
            data = f.read()
            if data == "":
                continue
            data_j = json.loads(data)
            for line in data_j:
                if not printi:
                    print(line)
                    printi = True
                update_buffer.append(line)
            
        # 上传es
        if len(update_buffer) > 0:
            write_to_es(update_buffer)


