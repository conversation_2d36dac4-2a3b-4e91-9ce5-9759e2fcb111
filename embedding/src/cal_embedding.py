# 计算向量

import json
import requests
import concurrent.futures

def post_request(url, json_data):
    """
    发送通用的 POST 请求

    :param url: 请求的 URL
    :param json_data: 请求的 JSON 格式数据
    :raises requests.exceptions.RequestException: 当请求过程中出现异常时抛出
    :return: API 返回的结果
    """
    try:
        response = requests.post(url, json=json_data)
        response.raise_for_status()  # 检查请求是否成功，不成功则抛出 HTTPError
        return response.json()
    except requests.exceptions.RequestException as e:
        raise requests.exceptions.RequestException(f"POST 请求失败: {str(e)}")

def process_single_content(content,url, id, title, urls, url_index, data_template):
    """
    处理单个 content，调用对应地址计算 embedding 并生成结果

    :param content: 待处理的内容
    :param id: 内容对应的 id
    :param title: 内容对应的标题
    :param urls: embedding 计算地址列表
    :param url_index: 当前使用的地址索引
    :param data_template: 请求数据的模板
    :return: 处理结果
    """
    url = urls[url_index]
    data = data_template.copy()
    data["payload"]["texts"] = [content]
    try:
        resp = post_request(url, data)
        # print(resp)
        payload = resp.get("payload", {})
        if "embedding" in payload:
            # gte向量
            embedding = resp["payload"]["embedding"][0] 
        else:
            # Qwen向量
            embedding = resp["payload"]["results"][0]["embedding"]
        return {
            "id": id,
            "ss":[{
                "id": id,
                "embedding": embedding,
                "span":[0, len(content)],
            }],
            "title": title,
            "content": content,
            "url": url,
        }
    except requests.exceptions.RequestException as e:
        print(f"调用 {url} 计算 {id} 的 embedding 时出错: {e}")
        return None

def load_balanced_concurrent_call(urls, data_template, input_file_path, output_file_path, max_workers):
    """
    负载均衡并发调用多个 embedding 计算地址

    :param urls: embedding 计算地址列表
    :param data_template: 请求数据的模板
    :param input_file_path: 输入文件路径
    :param output_file_path: 输出文件路径
    """
    with open(input_file_path, "r") as file, open(output_file_path, "w") as fp_W:
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = []
            for line in file:
                d = json.loads(line.strip())
                docs = d.get('data', [])
                for doc in docs:
                    content = doc.get('content', '')
                    title = doc.get('title', '')
                    url = doc.get('url', '')
                    if content:
                        future = executor.submit(
                            process_single_content,
                            title + '\n' + content, url, int(doc.get('id', 0)), title,
                            urls, len(futures) % len(urls), data_template
                        )
                        futures.append(future)

            for future in concurrent.futures.as_completed(futures):
                try:
                    result = future.result()
                    if result:
                        fp_W.write(json.dumps(result, ensure_ascii=False) + "\n")
                        fp_W.flush()  # 立即将缓冲区内容写入文件
                except Exception as e:
                    print(f"处理任务时出错: {e}")

# 多个 embedding 计算地址，需替换为实际地址
embedding_urls = [
    "http://10.103.240.171:31191/embedding/api/v2",
    # "http://another-embedding-url:port/embedding/api/v2",
    # 可添加更多地址
]

embedding_urls_Qwen = [
    'http://10.103.240.171:33050/embedding'
]

data_template = {
    "header": {
        "traceId": "wfliu3"
    },
    "payload": {
        "texts": []
    }
}

Qwen_data_template = {
    "header": {
        "traceId": "wfliu3"
    },
    "payload": {
        "model":"Qwen/Qwen3-Embedding-0.6B",
        "text": ""
    }
}

# input_file_path = "/data/lynxiao/SSTest/embedding/data/0612_v1/random100_new_health.json"
input_file_path = "/data/lynxiao/SSTest/embedding/data/0613_v1/random_1350_common.json"
output_file_path = "/data/lynxiao/SSTest/embedding/data/0612_v1/random100_new_health_embedding.json"
# qWen_output_file_path = "/data/lynxiao/SSTest/embedding/data/0612_v1/random100_new_health_QwenEmbedding.json"
qWen_output_file_path = "/data/lynxiao/SSTest/embedding/data/0613_v1/random_1350_common_QwenEmbedding.json"

# load_balanced_concurrent_call(embedding_urls, data_template, input_file_path, output_file_path)
load_balanced_concurrent_call(embedding_urls_Qwen, Qwen_data_template, input_file_path, qWen_output_file_path, max_workers=4)