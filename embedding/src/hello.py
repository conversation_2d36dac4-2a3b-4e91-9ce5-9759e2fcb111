import json
import os

# {'0~512': 2274, '512~800': 1697, '800~1000': 0, '1000~1500': 0}

def count_content_lengths(file_path):
    """
    统计文件中data里content数据的长度，并按指定区间统计数量

    :param file_path: 包含JSON数据的文件路径
    :return: 各区间的统计数量字典
    """
    # 初始化各区间的计数器
    length_counts = {
        "0~512": 0,
        "512~800": 0,
        "800~1000": 0,
        "1000~1500": 0
    }

    try:
        # 读取JSON文件
        with open(file_path, 'r', encoding='utf-8') as file:
            for line in file:
               
                data = json.loads(line.strip())
                # print(data)

                # 遍历data列表中的每个项
                for item in data.get('data', []):
                    content = item.get('content', '')
                    content_length = len(content)

                    # 根据content长度更新相应区间的计数器
                    if 0 <= content_length < 512:
                        length_counts["0~512"] += 1
                    elif 512 <= content_length < 800:
                        length_counts["512~800"] += 1
                    elif 800 <= content_length < 1000:
                        length_counts["800~1000"] += 1
                    elif 1000 <= content_length < 1500:
                        length_counts["1000~1500"] += 1

    except FileNotFoundError:
        print(f"文件 {file_path} 未找到。")
    except json.JSONDecodeError:
        print(f"文件 {file_path} 不是有效的JSON格式。")

    return length_counts

if __name__ == "__main__":
    file_path = "/data/lynxiao/SSTest/embedding/data/0612_v1/random100_new_health.json"
    result = count_content_lengths(file_path)
    print(result)