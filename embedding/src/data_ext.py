import json
import csv

# 输入 JSON 文件路径
input_file_path = "/data/lynxiao/SSTest/embedding/data/0612_v1/random100_new_health.json"
# 输出 CSV 文件路径，可按需修改
output_file_path = "/data/lynxiao/SSTest/embedding/data/0612_v1/random100_new_health_output.csv"

with open(input_file_path, "r") as f, open(output_file_path, "w", newline="", encoding="utf-8") as csvfile:
    # 定义 CSV 文件的表头
    fieldnames = ["id", "title", "content", "url"]
    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

    # 写入表头
    writer.writeheader()

    for line in f:
        data = json.loads(line.strip())
        docs = data.get("data", [])
        for doc in docs:
            try:
                id_val = int(doc.get("id", 0))
                content = doc.get("content", "")
                title = doc.get("title", "")
                url = doc.get("url", "")

                # 将数据写入 CSV 文件
                writer.writerow({
                    "id": id_val,
                    "title": title,
                    "content": content,
                    "url": url
                })
            except Exception as e:
                print(f"处理文档时出错: {e}")