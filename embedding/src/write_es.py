import json
import logging
from elasticsearch import Elasticsearch, helpers


schema = {
    "mappings": {
        "dynamic": "false",
        "properties": {
            "content": {
                "type": "keyword",
                "index": False,
                "doc_values": False
            },
            "id": {
                "type": "long"
            },
            "ss": {
                "type": "nested",
                "properties": {
                    "embedding": {
                        "type": "dense_vector",
                        "dims": 1024,
                        "index": True,
                        "similarity": "l2_norm",
                        "index_options": {
                            "type": "bbq_hnsw",
                            "m": 32,
                            "ef_construction": 256
                        }
                    },
                    "id": {
                        "type": "long"
                    },
                    "span": {
                        "type": "integer"
                    }
                }
            },
            "title": {
                "type": "keyword",
                "index": False,
                "doc_values": False
            },
            "url": {
                "type": "keyword"
            }
        }
    },
    "settings": {
        "index": {
            "refresh_interval": "5s",
            "number_of_shards": "24",
            "translog": {
                "flush_threshold_size": "16GB",
                "sync_interval": "60s",
                "durability": "async"
            },
            "merge": {
                "policy": {
                    "max_merged_segment": "10GB"
                }
            },
            "store": {
                "preload": [
                    "nvd",
                    "dvd",
                    "tim",
                    "doc",
                    "dim",
                    "vex",
                    "veb"
                ]
            },
            "analysis": {
                "analyzer": {
                    "my_custom_analyzer": {
                        "filter": [
                            "lowercase"
                        ],
                        "char_filter": [
                            "punctuation_filter"
                        ],
                        "type": "custom",
                        "tokenizer": "whitespace"
                    }
                },
                "char_filter": {
                    "punctuation_filter": {
                        "pattern": "[\\p{Punct}\\pP]",
                        "type": "pattern_replace",
                        "replacement": " "
                    }
                }
            },
            "number_of_replicas": 1
        }
    }
}


# 配置日志
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

def get_es_client():
    """创建可复用的ES连接"""
    return Elasticsearch(
        ["https://10.103.254.65:9200"],
        basic_auth=("elastic", "MifeNx0Sr+3rnkj58AbV"),
        verify_certs=False,
        ssl_show_warn=False
    )

def bulk_insert(es_client, actions):
    """带重试机制的批量插入"""
    try:
        if len(actions) == 1:
            # 单文档写入
            doc = actions[0]["_source"]
            try:
                doc["id"] = int(doc["id"])  # 确保 id 为整数类型
            except (ValueError, TypeError):
                logger.error(f"无法将 id {doc.get('id')} 转换为整数，跳过该文档")
                return
            response = es_client.index(index="embedding_medical_v2_health0612", id=doc["id"], document=doc)
            logger.info(f"单文档写入成功，结果: {response}")
            return 1, []
        else:
            success_count, errors = helpers.bulk(es_client, actions)
            if errors:
                for error in errors:
                    # 打印每个失败文档的错误详情
                    logger.error(f"插入失败文档详情: {error}")
            logger.info(f"成功插入 {success_count} 条文档，失败 {len(errors)} 条文档")
            return success_count, errors
    except helpers.BulkIndexError as e:
        logger.error(f"部分文档插入失败: {str(e)}")
        return 0, e.errors
    except Exception as e:
        logger.error(f"批量插入时发生未知错误: {str(e)}")
        return 0, []

def process_file(file_path, index_name, batch_size=1000):
    """处理文件并分批插入"""
    es = get_es_client()
    batch = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line in f:
                doc = json.loads(line.strip())
                try:
                    doc["id"] = int(doc["id"])  # 确保 id 为整数类型
                except (ValueError, TypeError):
                    logger.error(f"无法将 id {doc.get('id')} 转换为整数，跳过该文档")
                    continue
                
                # 直接在批量操作的动作里设置 _id，而不是添加到 _source 中
                batch.append({
                    "_op_type": "index",
                    "_index": index_name,
                    "_id": doc["id"],  # 直接设置 _id
                    "_source": doc  # 不再包含 _id 字段
                })
                
                if len(batch) >= batch_size:
                    bulk_insert(es, batch)
                    batch = []
            
            if batch:  # 处理剩余数据
                bulk_insert(es, batch)
    except Exception as e:
        logger.error(f"文件处理失败: {str(e)}")
        raise
    finally:
        es.close()


def create_or_recreate_index(es: Elasticsearch, index_name: str, schema: dict) -> bool:
    """
    创建或重建 Elasticsearch 索引
    :param es: Elasticsearch 客户端实例
    :param index_name: 索引名称
    :param schema: 索引的 Mapping 和 Settings 定义
    :return: 是否成功
    """
    try:
        # 1. 检查索引是否存在
        if es.indices.exists(index=index_name): 
            logger.info(f"索引 {index_name} 已存在，执行删除...")
            # 安全删除索引（忽略不存在的索引报错）
            es.indices.delete(
                index=index_name,
                ignore_unavailable=True,
                allow_no_indices=True
            )
            logger.info(f"索引 {index_name} 删除完成")

        # 2. 创建新索引
        es.indices.create(
            index=index_name,
            body=schema
        )
        logger.info(f"索引 {index_name} 创建成功")
        return True

    except ConnectionError:
        logger.error("连接到 Elasticsearch 失败，请检查网络或认证")
        return False
    except Exception as e:
        logger.error(f"未知错误: {str(e)}")
        return False

if __name__ == "__main__":
    es = get_es_client()
    create_or_recreate_index(es, "embedding_medical_v2_health0612_Qwen", schema)
    process_file("/data/lynxiao/SSTest/embedding/data/0612_v1/random100_new_health_QwenEmbedding.json", "embedding_medical_v2_health0612_Qwen")