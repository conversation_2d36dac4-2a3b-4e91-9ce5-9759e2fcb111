import time
import threading
import json
from typing import List

class Span:
    def __init__(self, name: str):
        self._name = name  # 操作名称
        self._start_time = int(time.time()*1000)  # 开始时间
        self._end_time = None  # 结束时间
        self._attributes = {}  # 自定义属性
        self._lock = threading.Lock()
        self._error = "OK"  # 状态，默认为 OK
        self._childs_span: List[Span] = []


    def set_name(self,name: str):
        with self._lock:
            self._name = name
            
    def add_span(self,name: str):
        c = Span(name)
        with self._lock:
            self._childs_span.append(c)
        return c

    def set_attribute(self, key, value):
        """设置自定义属性"""
        with self._lock:
            self._attributes[key] = value

    def set_error(self, status: str):
        """设置状态"""
        with self._lock:
            self._error = status

    def finish(self):
        """结束当前 Span"""
        with self._lock:
            """防止finish多次"""
            if self._end_time is None:
                self._end_time = int(time.time()*1000) 

    def duration(self):
        with self._lock:
            """计算持续时间"""
            if self._end_time is None:
                return None
            return self._end_time - self._start_time

    def to_dict(self) -> dict:
        duration = self.duration()
        with self._lock:
            """将 Span 转换为字典"""
            return {
                "name": self._name,
                "start_time": self._start_time,
                "end_time": self._end_time,
                "duration": duration,
                "attributes": self._attributes,
                "error": self._error,
                "children": [child.to_dict() for child in self._childs_span]
            }

    def to_json(self) -> str:
        return json.dumps(self.to_dict())

if __name__ == '__main__':
    s1 = Span("1")
    s1.finish()

    s2 = s1.add_span("2")
    s2.set_attribute("xx",666)
    s2.finish()
    print(s1.to_dict())
    print(s1.to_json())