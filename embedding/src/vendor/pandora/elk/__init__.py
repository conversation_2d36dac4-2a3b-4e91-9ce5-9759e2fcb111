from . import logger
from . import proto
from typing import Optional

global_logger = logger.ElkLogger()

def init_global_logger(name: str="default", log_path: str = "app.json", level: str = "INFO", rotate_size: int = 100 * 1024 * 1024, backup_count: int = 30):
    global global_logger
    if not global_logger.initial:
        global_logger = logger.ElkLogger()
        global_logger.init_log(name,log_path, level, rotate_size, backup_count)
        print("ElkLogger initialized",name,log_path, level, rotate_size, backup_count)

def get_global_logger():
    global global_logger
    return global_logger
