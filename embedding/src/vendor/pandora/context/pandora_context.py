from .context import Context
from ..span.span import Span
from fastapi import Request

class PandoraContext:
    def __init__(self,name: str):
        self.name = name
        self.context = Context()
        self.root_span = Span(name)

    @classmethod
    def with_fastapi(cls,request: Request,name: str):
        pc = cls(name)
        pc.root_span.set_attribute("path",request.url.path)
        pc.root_span.set_attribute("method",request.method)
        return pc
    

    def set_trace_info(self,trace_id: str,headerTag: str):
        self.context.set("traceId",trace_id)
        self.context.set("headerTag",headerTag)
        self.root_span.set_attribute("traceId",trace_id)
        self.root_span.set_attribute("headerTag",headerTag)