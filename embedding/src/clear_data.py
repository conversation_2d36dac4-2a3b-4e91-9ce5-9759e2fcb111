import os
import json

fp_w = open("/data/lynxiao/SSTest/embedding/data/0613_v1/random_1350_common_clean.json",'w', encoding='utf-8')
with open("/data/lynxiao/SSTest/embedding/data/0613_v1/random_1350_common.json","r") as f:
    for line in f:
        data = json.loads(line.strip())
        docs = data.get("data", [])
        for doc in docs:
            content = doc.get("content", "")
            title = doc.get("title", "")
            url = doc.get("url", "")
            id_val = doc.get("id", 0)
            
            # 确保每个文档有唯一ID
            if not id_val:
                print(f"文档缺少ID: {doc}")
                continue
            
            res = {
                "id": id_val,
                "url": url,
                "title": title,
                "content": content
            }
            fp_w.write(json.dumps(res, ensure_ascii=False) + '\n')

