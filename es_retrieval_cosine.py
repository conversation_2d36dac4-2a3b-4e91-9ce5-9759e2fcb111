#coding=utf-8

import json
import logging
# from tenacity import retry, stop_after_attempt, wait_exponential
from elasticsearch import Elasticsearch, helpers
import os
import time
import logging
import argparse
from concurrent.futures import ThreadPoolExecutor

st = time.time()
# 关闭 elasticsearch 模块的日志（默认是 INFO 级别）
logging.getLogger("elasticsearch").setLevel(logging.WARNING)  # WARNING 或更高
# 如果同时要关闭 transport 层的日志（可选）
logging.getLogger("elastic_transport").setLevel(logging.WARNING)

schema = {
    "mappings": {
        "dynamic": "false",
        "properties": {
            "content": {
                "type": "keyword",
                "index": False,
                "doc_values": False
            },
            "id": {
                "type": "long"
            },
            "embeddings": {
                "type": "nested",
                "properties": {
                    "content_terms": {
                        "type": "text",
                        "analyzer": "my_custom_analyzer"
                    },
                    "embedding": {
                        "type": "dense_vector",
                        "dims": 768,
                        "index": True,
                        "similarity": "cosine",
                        "index_options": {
                            "type": "bbq_hnsw",
                            "m": 32,
                            "ef_construction": 256
                        }
                    },
                    "id": {
                        "type": "long"
                    },
                    "span": {
                        "type": "integer"
                    }
                }
            },
            "title": {
                "type": "keyword",
                "index": False,
                "doc_values": False
            },
            "title_terms": {
                "type": "text",
                "analyzer": "my_custom_analyzer"
            },
            "url": {
                "type": "keyword"
            }
        }
    },
    "settings": {
        "index": {
            "refresh_interval": "5s",
            "number_of_shards": "24",
            "translog": {
                "flush_threshold_size": "16GB",
                "sync_interval": "60s",
                "durability": "async"
            },
            "merge": {
                "policy": {
                    "max_merged_segment": "10GB"
                }
            },
            "store": {
                "preload": [
                    "nvd",
                    "dvd",
                    "tim",
                    "doc",
                    "dim",
                    "vex",
                    "veb"
                ]
            },
            "analysis": {
                "analyzer": {
                    "my_custom_analyzer": {
                        "filter": [
                            "lowercase"
                        ],
                        "char_filter": [
                            "punctuation_filter"
                        ],
                        "type": "custom",
                        "tokenizer": "whitespace"
                    }
                },
                "char_filter": {
                    "punctuation_filter": {
                        "pattern": "[\\p{Punct}\\pP]",
                        "type": "pattern_replace",
                        "replacement": " "
                    }
                }
            },
            "number_of_replicas": 1
        }
    }
}

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

cluster = {
    0: ["https://10.103.254.65:9200"],
    1: ["https://10.103.254.31:19200", "https://10.103.254.31:19400", "https://10.103.254.31:19600"],
    2: ["https://10.103.254.57:9200", "https://10.103.254.58:9200", "https://10.103.254.59:9200", "https://10.103.254.60:9200", "https://10.103.254.61:9200", "https://10.103.254.62:9200"]
}


def get_es_client(id):
    """创建可复用的ES连接"""
    return Elasticsearch(
        cluster[id],
        http_auth=("elastic", "Aiaasxylx1.t"),
        verify_certs=False,
        ssl_show_warn=False,
        # 关闭所有请求日志（包括 HTTP 请求和响应）
        trace_logger=False  # 或通过 logging 更精细控制
    )

def build_insert_doc(data):
    # 将原始doc转为es中的doc类型
    tmp = data.copy()  # 避免改原始数据
    ss = tmp.get('ss', [])
    tmp['embeddings'] = []
    tmp['id'] = tmp['_id']

    # 虚拟嵌套结构
    for s in ss:
        t = s.copy()
        # t['embedding'] = tmp.get('embedding')
        # t['content_terms'] = tmp.get('content_terms')
        t.pop("content_terms", None)
        tmp['embeddings'].append(t)

    # 刪除指定字段
    for field in ['content_terms', 'title_terms', 'ss', 'post_time', "_id"]:
        tmp.pop(field, None)
    return tmp

def build_es_query(d):
    embedding = d['embeddings'][0]['embedding']
    if True:
        query_index = {"index": args.indexCode}
        query = {
            "size": 40,
            "min_score": 0,
            "query": {
                "nested": {
                    "path": "embeddings",
                    "query": {
                        "script_score": {
                            "query": {
                                "knn": {
                                    "field": "embeddings.embedding",
                                    "query_vector": embedding,
                                    "k": 40,
                                    "num_candidates": 256
                                }
                            },
                            "script": {
                                "source": "cosineSimilarity(params.queryVector, 'embeddings.embedding')+1.0",
                                "params": {
                                    "queryVector": embedding
                                }
                            }
                        }
                    }
                }
            }
        }
    return query_index, query

def search_bulk(query_docs):
    querys = []
    # index2DelDocList = {k:[] for k in range(len(query_docs))}
    # index2InsertDocList = {k:[] for k in range(len(query_docs))}
    for query_doc in query_docs:
        query_index, query = build_es_query(query_doc)
        querys.append(query_index)
        querys.append(query)
    response = es.msearch(body=querys)
    return response

def process_response(response, max_score=0.9):
    similarDocsList = []
    for single in response["responses"]:
        arr_tmp = []
        hit_docs = single['hits']['hits']
        for hit_doc in hit_docs:
            if hit_doc['_score'] > max_score and hit_doc['_score'] < 1:
                arr_tmp.append(hit_doc)
        similarDocsList.append(arr_tmp)
    return similarDocsList

def process_file_update(file_path, out_path, max_score):
    """处理文件并分批插入"""
    global CNT
    batch_docs = []
    out_file = open(out_path, "w", encoding="utf-8")
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line in f:
                if CNT % 10 == 0:
                    # 转换为分钟+秒
                    duration = time.time() - st
                    minutes = int(duration // 60)
                    seconds = int(duration % 60)
                    print(f"已处理{CNT}条数据,耗时: {minutes}分{seconds}秒")
                CNT += 1

                data = json.loads(line)
                if args.transData > 0:
                    data = build_insert_doc(data)
                batch_docs.append(data)
                if len(batch_docs) % 10 == 0:
                    response = search_bulk(batch_docs)
                    sim_docs = process_response(response, max_score)
                    for i,doc in enumerate(batch_docs):
                        id = batch_docs[i]["id"]
                        title = batch_docs[i]["title"]
                        content = batch_docs[i]["content"]
                        embedding = batch_docs[i]["embeddings"][0]["embedding"]
                        if len(sim_docs[i]) == 0:
                            continue
                        out_file.write(json.dumps({"id":id, "title":title, "content":content,
                                                  "embedding":embedding, "simdocs":sim_docs[i]},ensure_ascii=False) + "\n")
                    batch_docs = []
    except Exception as e:
        logger.error(f"文件处理失败: {str(e)}")
        raise

if __name__ == "__main__":
    # 创建解析器
    parser = argparse.ArgumentParser(description="es实时数据写入")

    # 添加参数
    parser.add_argument("src_dirs", nargs="+", help="输入数据目录列表")
    parser.add_argument("-o", "--outPath", type=str, help="输出文件路径")
    parser.add_argument("-i", "--indexCode", type=str, default="news_index_realtime",
                        help="输入索引库名称（默认: news_index_realtime)")
    parser.add_argument("-t", "--transData", type=int, help="是否进行数据格式转换,0:不需要, not 0:需要")
    parser.add_argument("-c", "--clusterId", type=int, help="0: 65集群、1：31集群")
    parser.add_argument("-s", "--maxScore", type=float, help="判断相关性的最大阈值")

    # 解析参数
    args = parser.parse_args()
    print(args)

    # 使用参数
    print(f"输入数据目录列表: {args.src_dirs}")
    print(f"输出文件路径: {args.outPath}")
    print(f"索引库名称: {args.indexCode}")
    print(f"是否进行数据转换: {args.transData}")
    print(f"集群id: {args.clusterId}")
    print(f"相关性文档的最大阈值: {args.maxScore}")

    CNT = 0
    es = get_es_client(args.clusterId)
    for dir in args.src_dirs:
        files = os.listdir(dir)
        files = [os.path.join(dir, file) for file in files]
        for file in files:
            process_file_update(file, args.outPath, args.maxScore)
