import pandas as pd
import os
import sys
import pandas as pd
import openpyxl
import xlrd
import csv
from pathlib import Path
import json

def split_excel_by_sheet(file_path):
    """将Excel文件中的每个sheet拆分为独立的Excel文件"""
    # 获取文件名(不含扩展名)和扩展名
    file_name_without_ext, file_ext = os.path.splitext(os.path.basename(file_path))
    dir_path = os.path.dirname(file_path)
    print(dir_path)
    
    # 读取Excel文件
    xls = pd.ExcelFile(file_path)
    
    # 获取所有sheet名称
    sheet_names = xls.sheet_names
    
    # 遍历每个sheet
    for sheet_name in sheet_names:
        # 读取当前sheet数据
        df = xls.parse(sheet_name)
        
        # 生成新文件名
        new_file_name = f"{file_name_without_ext}-{sheet_name}.xlsx"
        print(f"正在处理: {new_file_name}")
        
        # 保存为新Excel文件
        df.to_excel(f"{dir_path}/{new_file_name}", index=False)
        print(f"已保存: {new_file_name}")

def split_excel():
    # 检查是否提供了文件路径
    if len(sys.argv) < 2:
        print("用法: python excel_sheet_splitter.py <Excel文件路径>")
        sys.exit(1)
    
    # 获取Excel文件路径
    excel_file_path = sys.argv[1]
    
    # 检查文件是否存在
    if not os.path.exists(excel_file_path):
        print(f"错误: 文件 '{excel_file_path}' 不存在")
        sys.exit(1)
    
    # 检查文件是否为Excel文件
    valid_extensions = ['.xlsx', '.xls', '.xlsm']
    if not any(excel_file_path.lower().endswith(ext) for ext in valid_extensions):
        print(f"错误: 文件 '{excel_file_path}' 不是有效的Excel文件")
        sys.exit(1)
    
    # 执行拆分操作
    split_excel_by_sheet(excel_file_path)


# 方法1: 使用pandas读取Excel并获取每行数据
def read_excel_with_pandas(file_path):
    results = []
    baseQuery = ""
    id = 0
    result = {}
    count = 0
    """使用pandas读取Excel文件并逐行处理数据"""
    # 读取Excel文件
    df = pd.read_excel(file_path)
    
    # 方法1.2: 使用itertuples()遍历(性能更好)
    print("\n使用itertuples()遍历:")
    for row in df.itertuples(index=True, name='PandasRow'):
        rowId = row.Index
        data = row._asdict()  # 将行数据转换为字典
        # print(f"行索引: {rowId}, 数据: {data}")
        query = data.get("query", "")
        if query != baseQuery:
            result["count"] = len(result["urls"]) if "urls" in result else 0
            count += result["count"]
            results.append(result)
            result = {}
            result['urls'] = []
            baseQuery = query
            id += 1
            
        result["id"] = id
        result["query"] = query
        result["urls"].append(data.get("url", ""))
    result["count"] = len(result["urls"]) if "urls" in result else 0
    count += result["count"]
    results.append(result)
    print(len(results))
    print(count)
    return results



if __name__ == "__main__":
    # split_excel()
    # 检查是否提供了文件路径
    if len(sys.argv) < 2:
        print("用法: python excel_sheet_splitter.py <Excel文件路径>")
        sys.exit(1)
    
    # 获取Excel文件路径
    excel_file_path = sys.argv[1]
    
    # 检查文件是否存在
    if not os.path.exists(excel_file_path):
        print(f"错误: 文件 '{excel_file_path}' 不存在")
        sys.exit(1)

     # 检查文件是否为Excel文件
    valid_extensions = ['.xlsx', '.xls', '.xlsm']
    if not any(excel_file_path.lower().endswith(ext) for ext in valid_extensions):
        print(f"错误: 文件 '{excel_file_path}' 不是有效的Excel文件")
        sys.exit(1)
    

    results = read_excel_with_pandas(excel_file_path)
    dir_path = os.path.dirname(excel_file_path)
    # 写入文件（每行一个 JSON 对象）
    with open(f"{dir_path}/{excel_file_path.split('/')[-1].split('.')[0] + '.json'}", "w", encoding="utf-8") as f:
        for item in results:
            # 将单个 JSON 对象转换为字符串并写入行（确保非 ASCII 字符正常显示）
            json_str = json.dumps(item, ensure_ascii=False)
            f.write(json_str + "\n")


    
    
