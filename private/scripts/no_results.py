import os 
import json 

refuse_querys = []
with open("/data/lynxiao/SSTest/private/data/0610_v1/拒识query.txt", "r") as f:
    for line in f:
        refuse_querys.append(line.strip())

gch_noResults = []
hf_noResults = []


with open("/data/lynxiao/SSTest/private/data/0610_v1/gch无结果数据.json", 'r')  as f:
    for line in f:
        data = json.loads(line.strip())
        # print(data["payload"]["output"]['data'])
        gch_noResults.append(data["payload"]["output"]["data"][0]["query"])

with open("/data/lynxiao/SSTest/private/data/0610_v1/合肥无结果数据.json", 'r')  as f:
    for line in f:
        data = json.loads(line.strip())
        if data["payload"]["output"]["routing_refuse"]:
            # print(data["payload"])
            # refuse_querys.append(data["payload"]["query"])
            pass
        else:
            hf_noResults.append(data["payload"]["output"]["data"][0]["query"])

print('*'*30)
print(len(refuse_querys))
print(len(gch_noResults))
print(len(hf_noResults))


# 找出gch_noResults中不在refuse_querys也不在hf_noResults中的字符串
unique_in_gch = [item for item in gch_noResults if item not in refuse_querys and item not in hf_noResults]
print("gch_noResults中不在refuse_querys也不在hf_noResults中的字符串:")
print(unique_in_gch)

# 找出hf_noResults中不在refuse_querys也不在gch_noResults中的字符串
unique_in_hf = [item for item in hf_noResults if item not in refuse_querys and item not in gch_noResults]
print("hf_noResults中不在refuse_querys也不在gch_noResults中的字符串:")
print(unique_in_hf)