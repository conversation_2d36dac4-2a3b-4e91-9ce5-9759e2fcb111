import os
import json


refuse_querys = []
with open("/data/lynxiao/SSTest/private/data/0610_v1/拒识query.txt", "r") as f:
    for line in f:
        refuse_querys.append(line.strip())

data1 = {}
data2 = {}

querys = set()

with open("/data/lynxiao/SSTest/private/data/0609_v1/一致性统计结果-hf.json", "r") as f:
    for line in f:
        data = json.loads(line.strip())
        data1[data["query"]] = data["urls"]
        querys.add(data["query"])

with open("/data/lynxiao/SSTest/private/data/0609_v1/一致性统计结果-gch.json", "r") as f:
    for line in f:
        data = json.loads(line.strip())
        data2[data["query"]] = data["urls"]
        querys.add(data["query"])

fp_w1 = open("/data/lynxiao/SSTest/private/data/0609_v1/不一致的query.txt", "w")
fp_w2 = open("/data/lynxiao/SSTest/private/data/0609_v1/一致的query.txt", "w")
with open("/data/lynxiao/SSTest/private/data/0609_v1/一致性统计结果-queries.txt", "w") as f:
    
    for query in querys:
        if query in refuse_querys:
            continue
        result = {}
        hf_res = data1.get(query, [])
        gch_res = data2.get(query, [])
         # 比较 hf_res 和 gch_res 内容是否一致
        is_consistent = hf_res == gch_res
        result[query] = {
            "hf": hf_res,
            "gch": gch_res,
            "is_consistent": is_consistent
        }
        if is_consistent:
            fp_w2.write(json.dumps(result,ensure_ascii=False) + "\n")
        else:
            fp_w1.write(json.dumps(result,ensure_ascii=False) + "\n")
        f.write(json.dumps(result,ensure_ascii=False) + "\n")


# with open("/data/lynxiao/SSTest/private/data/0609_v1/一致性统计结果-queries.txt", "r") as f:
#     for line in f:
#         data = json.loads(line.strip())
#         for k,v in data.items():
#             print(v)
#             if not v["is_consistent"]:
#                 fp_w1.write(line)
#             else:
#                 fp_w2.write(line)




