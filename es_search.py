import json
import logging
# from tenacity import retry, stop_after_attempt, wait_exponential
from elasticsearch import Elasticsearch, helpers
import os
import time
import logging
import argparse
from concurrent.futures import ThreadPoolExecutor

query = {
            "size": 40,
            "min_score": 0,
            "query": {
                "nested": {
                    "path": "embeddings",
                    "query": {
                        "script_score": {
                            "query": {
                                "knn": {
                                    "field": "embeddings.embedding",
                                    "query_vector": [],
                                    "k": 40,
                                    "num_candidates": 256
                                }
                            },
                            "script": {
                                "source":  "1/(l2norm(params.queryVector, 'ss.vector')+1)",
                                "params": {
                                    "queryVector": []
                                }
                            }
                        }
                    }
                }
            }
        }

cluster = {
    0: ["https://*************:9200"],
    1: ["https://*************:19200", "https://*************:19400", "https://*************:19600"],
    2: ["https://*************:9200", "https://*************:9200", "https://*************:9200", "https://*************:9200", "https://*************:9200", "https://*************:9200"]
}


def get_es_client(id):
    """创建可复用的ES连接"""
    return Elasticsearch(
        cluster[id],
        http_auth=("elastic", "Aiaasxylx1.t"),
        verify_certs=False,
        ssl_show_warn=False,
        
    )


es_client = get_es_client(2)
print(es_client)